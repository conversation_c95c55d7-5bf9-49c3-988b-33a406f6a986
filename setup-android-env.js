/**
 * Android Environment Setup Script
 * Automatically detects and sets Android SDK environment variables for cross-platform compatibility
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

class AndroidEnvironmentSetup {
    
    constructor() {
        this.platform = os.platform();
        this.isWindows = this.platform === 'win32';
        this.isMac = this.platform === 'darwin';
        this.isLinux = this.platform === 'linux';
    }

    /**
     * Get common Android SDK installation paths based on platform
     */
    getCommonAndroidPaths() {
        const userHome = os.homedir();
        const commonPaths = [];

        if (this.isWindows) {
            commonPaths.push(
                path.join(userHome, 'AppData', 'Local', 'Android', 'Sdk'),
                path.join(userHome, 'Android', 'Sdk'),
                'C:\\Android\\Sdk',
                'C:\\Program Files\\Android\\Android Studio\\sdk',
                'C:\\Program Files (x86)\\Android\\android-sdk'
            );
        } else if (this.isMac) {
            commonPaths.push(
                path.join(userHome, 'Library', 'Android', 'sdk'),
                path.join(userHome, 'Android', 'Sdk'),
                '/usr/local/android-sdk',
                '/opt/android-sdk'
            );
        } else if (this.isLinux) {
            commonPaths.push(
                path.join(userHome, 'Android', 'Sdk'),
                path.join(userHome, 'android-sdk'),
                '/usr/local/android-sdk',
                '/opt/android-sdk',
                '/usr/lib/android-sdk'
            );
        }

        return commonPaths;
    }

    /**
     * Find Android SDK installation
     */
    findAndroidSdk() {
        console.log('🔍 Searching for Android SDK installation...');

        // Check existing environment variables first
        const existingAndroidHome = process.env.ANDROID_HOME || process.env.ANDROID_SDK_ROOT;
        if (existingAndroidHome && fs.existsSync(existingAndroidHome)) {
            console.log(`✅ Found existing Android SDK: ${existingAndroidHome}`);
            return existingAndroidHome;
        }

        // Search common installation paths
        const commonPaths = this.getCommonAndroidPaths();
        
        for (const sdkPath of commonPaths) {
            if (fs.existsSync(sdkPath)) {
                // Verify it's a valid Android SDK by checking for key directories
                const platformToolsPath = path.join(sdkPath, 'platform-tools');
                const buildToolsPath = path.join(sdkPath, 'build-tools');
                
                if (fs.existsSync(platformToolsPath) && fs.existsSync(buildToolsPath)) {
                    console.log(`✅ Found Android SDK at: ${sdkPath}`);
                    return sdkPath;
                }
            }
        }

        console.log('❌ Android SDK not found in common locations');
        return null;
    }

    /**
     * Setup Android environment variables
     */
    setupAndroidEnvironment() {
        console.log('🚀 Setting up Android environment...');

        const androidSdkPath = this.findAndroidSdk();
        
        if (!androidSdkPath) {
            console.error('❌ Android SDK not found. Please install Android SDK or set ANDROID_HOME manually.');
            console.log('\n📋 Installation Instructions:');
            console.log('1. Download Android Studio from: https://developer.android.com/studio');
            console.log('2. Install Android Studio and SDK');
            console.log('3. Set ANDROID_HOME environment variable to SDK path');
            console.log('4. Add platform-tools to PATH');
            return false;
        }

        // Set environment variables
        process.env.ANDROID_HOME = androidSdkPath;
        process.env.ANDROID_SDK_ROOT = androidSdkPath;
        
        // Add platform-tools and tools to PATH
        const platformToolsPath = path.join(androidSdkPath, 'platform-tools');
        const toolsPath = path.join(androidSdkPath, 'tools');
        const toolsBinPath = path.join(androidSdkPath, 'tools', 'bin');
        
        const currentPath = process.env.PATH || '';
        const pathSeparator = this.isWindows ? ';' : ':';
        
        const newPathEntries = [platformToolsPath, toolsPath, toolsBinPath]
            .filter(p => fs.existsSync(p))
            .filter(p => !currentPath.includes(p));
        
        if (newPathEntries.length > 0) {
            process.env.PATH = currentPath + pathSeparator + newPathEntries.join(pathSeparator);
        }

        console.log(`✅ ANDROID_HOME set to: ${androidSdkPath}`);
        console.log(`✅ ANDROID_SDK_ROOT set to: ${androidSdkPath}`);
        console.log(`✅ Added to PATH: ${newPathEntries.join(', ')}`);

        return true;
    }

    /**
     * Verify Android setup
     */
    async verifyAndroidSetup() {
        console.log('🔍 Verifying Android setup...');

        const checks = {
            androidHome: !!process.env.ANDROID_HOME,
            androidSdkRoot: !!process.env.ANDROID_SDK_ROOT,
            adbAvailable: false,
            platformToolsExists: false,
            buildToolsExists: false
        };

        // Check if ADB is available
        try {
            const { exec } = require('child_process');
            await new Promise((resolve, reject) => {
                exec('adb version', (error, stdout, stderr) => {
                    if (error) {
                        reject(error);
                    } else {
                        checks.adbAvailable = true;
                        resolve(stdout);
                    }
                });
            });
        } catch (error) {
            console.log('⚠️  ADB not available in PATH');
        }

        // Check SDK directories
        if (process.env.ANDROID_HOME) {
            const sdkPath = process.env.ANDROID_HOME;
            checks.platformToolsExists = fs.existsSync(path.join(sdkPath, 'platform-tools'));
            checks.buildToolsExists = fs.existsSync(path.join(sdkPath, 'build-tools'));
        }

        // Print verification results
        console.log('\n📋 Android Setup Verification:');
        console.log(`   ANDROID_HOME: ${checks.androidHome ? '✅' : '❌'} ${process.env.ANDROID_HOME || 'Not set'}`);
        console.log(`   ANDROID_SDK_ROOT: ${checks.androidSdkRoot ? '✅' : '❌'} ${process.env.ANDROID_SDK_ROOT || 'Not set'}`);
        console.log(`   ADB Available: ${checks.adbAvailable ? '✅' : '❌'}`);
        console.log(`   Platform Tools: ${checks.platformToolsExists ? '✅' : '❌'}`);
        console.log(`   Build Tools: ${checks.buildToolsExists ? '✅' : '❌'}`);

        const allChecksPass = Object.values(checks).every(check => check === true);
        
        if (allChecksPass) {
            console.log('\n🎉 Android environment setup is complete and verified!');
        } else {
            console.log('\n⚠️  Some Android setup issues detected. Please review the above.');
        }

        return allChecksPass;
    }

    /**
     * Generate environment setup report
     */
    generateSetupReport() {
        const report = {
            timestamp: new Date().toISOString(),
            platform: this.platform,
            androidHome: process.env.ANDROID_HOME,
            androidSdkRoot: process.env.ANDROID_SDK_ROOT,
            pathEntries: process.env.PATH ? process.env.PATH.split(this.isWindows ? ';' : ':') : [],
            commonPaths: this.getCommonAndroidPaths(),
            setupSuccessful: !!(process.env.ANDROID_HOME && process.env.ANDROID_SDK_ROOT)
        };

        // Save report
        const reportPath = './android-setup-report.json';
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`📄 Setup report saved to: ${reportPath}`);

        return report;
    }
}

module.exports = AndroidEnvironmentSetup;

// If run directly, execute setup
if (require.main === module) {
    const setup = new AndroidEnvironmentSetup();
    
    (async () => {
        console.log('🔧 Android Environment Setup Tool');
        console.log('==================================');
        
        const setupSuccess = setup.setupAndroidEnvironment();
        
        if (setupSuccess) {
            await setup.verifyAndroidSetup();
            setup.generateSetupReport();
        } else {
            console.log('\n❌ Android environment setup failed. Please install Android SDK manually.');
            process.exit(1);
        }
    })();
}
