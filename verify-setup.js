/**
 * Setup Verification Script
 * Checks all prerequisites for running Appium tests
 */

const AndroidEnvironmentSetup = require('./setup-android-env');
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

class SetupVerifier {
    
    constructor() {
        this.checks = {
            nodejs: false,
            npm: false,
            appium: false,
            androidSdk: false,
            adb: false,
            apkFile: false,
            dependencies: false,
            appiumServer: false
        };
        this.results = [];
    }

    /**
     * Execute command and return result
     */
    async executeCommand(command) {
        return new Promise((resolve) => {
            exec(command, (error, stdout, stderr) => {
                resolve({
                    success: !error,
                    stdout: stdout?.trim() || '',
                    stderr: stderr?.trim() || '',
                    error: error?.message || null
                });
            });
        });
    }

    /**
     * Check Node.js installation
     */
    async checkNodeJS() {
        console.log('🔍 Checking Node.js...');
        const result = await this.executeCommand('node --version');
        
        if (result.success) {
            this.checks.nodejs = true;
            this.results.push({
                check: 'Node.js',
                status: 'PASS',
                details: `Version: ${result.stdout}`,
                icon: '✅'
            });
        } else {
            this.results.push({
                check: 'Node.js',
                status: 'FAIL',
                details: 'Node.js not found. Please install Node.js from https://nodejs.org/',
                icon: '❌'
            });
        }
    }

    /**
     * Check NPM installation
     */
    async checkNPM() {
        console.log('🔍 Checking NPM...');
        const result = await this.executeCommand('npm --version');
        
        if (result.success) {
            this.checks.npm = true;
            this.results.push({
                check: 'NPM',
                status: 'PASS',
                details: `Version: ${result.stdout}`,
                icon: '✅'
            });
        } else {
            this.results.push({
                check: 'NPM',
                status: 'FAIL',
                details: 'NPM not found. Please install Node.js which includes NPM.',
                icon: '❌'
            });
        }
    }

    /**
     * Check Appium installation
     */
    async checkAppium() {
        console.log('🔍 Checking Appium...');
        const result = await this.executeCommand('appium --version');
        
        if (result.success) {
            this.checks.appium = true;
            this.results.push({
                check: 'Appium',
                status: 'PASS',
                details: `Version: ${result.stdout}`,
                icon: '✅'
            });
        } else {
            this.results.push({
                check: 'Appium',
                status: 'FAIL',
                details: 'Appium not found. Install with: npm install -g appium',
                icon: '❌'
            });
        }
    }

    /**
     * Check Android SDK setup
     */
    async checkAndroidSDK() {
        console.log('🔍 Checking Android SDK...');
        
        const androidSetup = new AndroidEnvironmentSetup();
        const setupSuccess = androidSetup.setupAndroidEnvironment();
        
        if (setupSuccess) {
            this.checks.androidSdk = true;
            this.results.push({
                check: 'Android SDK',
                status: 'PASS',
                details: `ANDROID_HOME: ${process.env.ANDROID_HOME}`,
                icon: '✅'
            });
        } else {
            this.results.push({
                check: 'Android SDK',
                status: 'FAIL',
                details: 'Android SDK not found. Please install Android Studio and set ANDROID_HOME.',
                icon: '❌'
            });
        }
    }

    /**
     * Check ADB availability
     */
    async checkADB() {
        console.log('🔍 Checking ADB...');
        const result = await this.executeCommand('adb version');
        
        if (result.success) {
            this.checks.adb = true;
            this.results.push({
                check: 'ADB',
                status: 'PASS',
                details: 'ADB is available in PATH',
                icon: '✅'
            });
        } else {
            this.results.push({
                check: 'ADB',
                status: 'FAIL',
                details: 'ADB not found in PATH. Ensure Android SDK platform-tools is in PATH.',
                icon: '❌'
            });
        }
    }

    /**
     * Check APK file exists
     */
    async checkAPKFile() {
        console.log('🔍 Checking APK file...');
        const apkPath = path.join(process.cwd(), 'app-dev-release.apk');
        
        if (fs.existsSync(apkPath)) {
            this.checks.apkFile = true;
            const stats = fs.statSync(apkPath);
            this.results.push({
                check: 'APK File',
                status: 'PASS',
                details: `Found: app-dev-release.apk (${(stats.size / 1024 / 1024).toFixed(2)} MB)`,
                icon: '✅'
            });
        } else {
            this.results.push({
                check: 'APK File',
                status: 'FAIL',
                details: 'app-dev-release.apk not found in project root',
                icon: '❌'
            });
        }
    }

    /**
     * Check project dependencies
     */
    async checkDependencies() {
        console.log('🔍 Checking project dependencies...');
        
        if (fs.existsSync('node_modules') && fs.existsSync('package.json')) {
            this.checks.dependencies = true;
            this.results.push({
                check: 'Dependencies',
                status: 'PASS',
                details: 'Node modules installed',
                icon: '✅'
            });
        } else {
            this.results.push({
                check: 'Dependencies',
                status: 'FAIL',
                details: 'Dependencies not installed. Run: npm install',
                icon: '❌'
            });
        }
    }

    /**
     * Check if Appium server is running
     */
    async checkAppiumServer() {
        console.log('🔍 Checking Appium server...');
        
        try {
            const http = require('http');
            const options = {
                hostname: 'localhost',
                port: 4723,
                path: '/status',
                method: 'GET',
                timeout: 5000
            };

            const result = await new Promise((resolve) => {
                const req = http.request(options, (res) => {
                    resolve({ success: true, statusCode: res.statusCode });
                });
                
                req.on('error', () => {
                    resolve({ success: false });
                });
                
                req.on('timeout', () => {
                    resolve({ success: false });
                });
                
                req.end();
            });

            if (result.success) {
                this.checks.appiumServer = true;
                this.results.push({
                    check: 'Appium Server',
                    status: 'PASS',
                    details: 'Appium server is running on localhost:4723',
                    icon: '✅'
                });
            } else {
                this.results.push({
                    check: 'Appium Server',
                    status: 'FAIL',
                    details: 'Appium server not running. Start with: npm run appium:start',
                    icon: '❌'
                });
            }
        } catch (error) {
            this.results.push({
                check: 'Appium Server',
                status: 'FAIL',
                details: 'Could not connect to Appium server',
                icon: '❌'
            });
        }
    }

    /**
     * Run all verification checks
     */
    async runAllChecks() {
        console.log('🔧 Setup Verification Tool');
        console.log('===========================\n');

        await this.checkNodeJS();
        await this.checkNPM();
        await this.checkAppium();
        await this.checkAndroidSDK();
        await this.checkADB();
        await this.checkAPKFile();
        await this.checkDependencies();
        await this.checkAppiumServer();

        this.printResults();
        this.generateReport();
        
        return this.isSetupComplete();
    }

    /**
     * Print verification results
     */
    printResults() {
        console.log('\n📋 Setup Verification Results:');
        console.log('================================');
        
        this.results.forEach(result => {
            console.log(`${result.icon} ${result.check}: ${result.status}`);
            console.log(`   ${result.details}\n`);
        });
    }

    /**
     * Check if setup is complete
     */
    isSetupComplete() {
        const passedChecks = this.results.filter(r => r.status === 'PASS').length;
        const totalChecks = this.results.length;
        const isComplete = passedChecks === totalChecks;
        
        console.log(`\n🎯 Setup Status: ${passedChecks}/${totalChecks} checks passed`);
        
        if (isComplete) {
            console.log('🎉 All checks passed! You can run the tests now.');
        } else {
            console.log('⚠️  Some issues need to be resolved before running tests.');
            console.log('\n📝 Next Steps:');
            this.results.filter(r => r.status === 'FAIL').forEach(result => {
                console.log(`   • ${result.details}`);
            });
        }
        
        return isComplete;
    }

    /**
     * Generate verification report
     */
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            platform: process.platform,
            results: this.results,
            summary: {
                total: this.results.length,
                passed: this.results.filter(r => r.status === 'PASS').length,
                failed: this.results.filter(r => r.status === 'FAIL').length,
                setupComplete: this.isSetupComplete()
            }
        };

        fs.writeFileSync('./setup-verification-report.json', JSON.stringify(report, null, 2));
        console.log('\n📄 Verification report saved to: setup-verification-report.json');
    }
}

// Run verification if script is executed directly
if (require.main === module) {
    const verifier = new SetupVerifier();
    verifier.runAllChecks().then(isComplete => {
        process.exit(isComplete ? 0 : 1);
    });
}

module.exports = SetupVerifier;
