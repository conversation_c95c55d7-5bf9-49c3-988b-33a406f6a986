# Production Data Collection App - Single Comprehensive Test

This project contains a single comprehensive test for the Production Data Collection mobile app.

## Test Overview

The test performs the following actions:
1. **Open the app** - Launches and verifies the main screen
2. **Click Specimen button** → Screen changes → Press back
3. **Click Dome button** → Expands to show Harvesting and Media Moisture items (no screen change)
4. **Click Harvesting button** → Screen changes → Press back
5. **Click Media Moisture button** → Screen changes → Press back

## Project Structure

```
├── test/
│   ├── pageobjects/
│   │   └── ProductionDataPage.js    # Page object with element selectors
│   └── specs/
│       └── single-comprehensive-test.js    # Single test file
├── app-dev-release.apk              # APK file to test
├── wdio.conf.js                     # WebDriverIO configuration
└── package.json                     # Dependencies and scripts
```

## Prerequisites

1. **Node.js** (v14 or higher)
2. **Android SDK** with ADB
3. **Appium Server** (v2.x)
4. **Android Emulator** or physical Android device

## Setup

1. Install dependencies:
```bash
npm install
```

2. Start Appium server:
```bash
npm run appium:start
```

3. Ensure Android emulator is running or device is connected

## Running the Test

Run the comprehensive test with HTML report generation:
```bash
npm run test:comprehensive
```

Or run the test directly with WebDriverIO:
```bash
npm run test:wdio
```

Or run the original test:
```bash
npm test
```

This will execute the test file `test/specs/single-comprehensive-test.js` which contains all the required test scenarios with the **FIXED** Dome expansion logic.

## 🎯 Fixed Test Logic

The test now properly handles the Dome expansion behavior:

1. **Specimen**: Direct navigation button (Click → New Screen → Back)
2. **Dome**: Expansion button (Click → Expands to show Harvesting & Media Moisture)
3. **Harvesting**: Sub-item under Dome (Expand Dome → Click → New Screen → Back)
4. **Media Moisture**: Sub-item under Dome (Expand Dome → Click → New Screen → Back)

### Key Fix
After navigating back from any screen, the Dome section collapses. The test now:
- Automatically checks if Dome is expanded before accessing Harvesting/Media Moisture
- Re-expands Dome when needed using the `ensureDomeExpanded()` method
- Properly tracks the expansion state

## Test Results & Reports

- **Screenshots**: Saved in `./screenshots/` directory with timestamps
- **HTML Report**: Comprehensive report generated at `./test-results/comprehensive_test_report.html`
- **JSON Results**: Detailed test data saved in `./test-results/test_results.json`
- **Console Output**: Real-time test progress and results
- **Summary Report**: Test run summary in `./test-results/test_run_summary.json`

### HTML Report Features
- ✅ Beautiful, responsive design
- 📊 Test summary with pass/fail statistics
- 🕐 Execution timing and duration
- 📸 Screenshot references
- 🔍 Detailed test step information
- ❌ Error details for failed tests
- 📱 Mobile-friendly layout

## Configuration

The test is configured in `wdio.conf.js` to:
- Use Android platform
- Target the `app-dev-release.apk` file
- Run on Android Emulator (or modify for real device)
- Use UiAutomator2 automation engine



